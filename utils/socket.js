import { getClientIp } from '@supercharge/request-ip/dist/index.js';
import { Server } from 'socket.io';

import { verifyToken } from '#utils/auth.js';
import { toLogDate } from '#utils/dates.js';
import Logger from '#utils/logger.js';

function initSocket(server, app) {
  const io = new Server(server, {
    path: '/socket',
    serveClient: false,
    pingInterval: 5000,
    pingTimeout: 12000,
    transports: ['websocket'],
  });
  Logger.info(`Socket.IO initialized on path /socket`);

  // Listen to connections on the 'notifications' namespace
  io.of('/notifications').on('connection', function (socket) {
    const clientIp = getClientIp(socket.request);

    Logger.info(
      `[${toLogDate(new Date())}]`,
      clientIp,
      'has connected to the socket'
    );

    // When a user tries to subscribe, verify the token authenticity
    socket.on('subscribe', async function () {
      // Verify authentication using cookie from handshake
      const userId = await verifyAuth({ socket });

      if (!userId) {
        Logger.error(
          `[${toLogDate(new Date())}]`,
          clientIp,
          'Socket connection denied!'
        );
        socket.disconnect(true);
      } else {
        // If the token is valid, subscribe the socket to the userId's room
        socket.join(userId);
        Logger.info(
          `[${toLogDate(new Date())}]`,
          clientIp,
          'has subscribed to notifications for userId',
          userId
        );
      }
    });

    // When a user tries to unsubscribe, just do it
    socket.on('unsubscribe', async function (userId) {
      socket.leave(userId);
      Logger.info(
        `[${toLogDate(new Date())}]`,
        clientIp,
        'has unsubscribed to notifications for userId',
        userId
      );
    });

    // Log when a socket disconnects, for testing
    socket.on('disconnect', function () {
      Logger.info(
        `[${toLogDate(new Date())}]`,
        clientIp,
        'has disconnected from the socket'
      );
    });
  });

  const sockets = app.get('sockets');
  const socketEntries = Object.entries(sockets) || [];

  // Register socket namespaces
  if (socketEntries.length) {
    // If sockets already exist, merge the new io instance
    socketEntries.forEach(([namespace, socketInitFn]) => {
      if (typeof socketInitFn === 'function') {
        try {
          io.of(`${namespace}`).on('connection', socketInitFn);
          Logger.info(
            `Socket namespace "${namespace}" initialized successfully.`
          );
        } catch (error) {
          Logger.error(
            `Error initializing socket namespace "${namespace}":`,
            error
          );
        }
      } else {
        Logger.warn(`Socket namespace "${namespace}" is not a function.`);
      }
    });
  }

  // Store io in app for access elsewhere
  app.set('socketio', io);
}

export async function verifyAuth({ socket }) {
  try {
    const cookieHeader = socket.request.headers.cookie;
    if (!cookieHeader) {
      Logger.warn('No cookies in handshake');
      return false;
    }

    const cookies = parseCookies(cookieHeader);
    const authorization = cookies['hope-jwt'];
    if (!authorization) {
      Logger.warn('No hope-jwt cookie found');
      return false;
    }

    const payload = await verifyToken(authorization);
    if (!payload || !payload.id) {
      Logger.warn('Invalid JWT payload');
      return false;
    }

    return payload.id;
  } catch (error) {
    Logger.error('Error parsing JWT from cookie:', error);
    return false;
  }
}

export function parseCookies(cookieHeader) {
  const cookies = {};
  if (!cookieHeader) return cookies;
  cookieHeader.split(';').forEach((cookie) => {
    const [name, value] = cookie.split('=').map((part) => part.trim());
    cookies[name] = value;
  });
  return cookies;
}

export default initSocket;
