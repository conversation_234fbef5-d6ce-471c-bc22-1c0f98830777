import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const eventWorkshopSchema = SchemaFactory({
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
  },
  contributor: {
    type: String,
  },
  status: {
    type: String,
  },
  additionalService: {
    type: Boolean,
  },
  minParticipants: {
    type: Number,
  },
  maxParticipants: {
    type: Number,
  },
  countParticipants: {
    type: Number,
  },
  price: {
    type: Number,
    default: null,
  },
  entity: {
    type: mongoose.Types.ObjectId,
    ref: 'Entity',
    required: true,
  },
  event: {
    type: mongoose.Types.ObjectId,
    ref: 'Event',
    required: true,
  },
  importID: {
    type: mongoose.SchemaTypes.Mixed,
  },
});

eventWorkshopSchema.index({ title: 1 });
eventWorkshopSchema.index({ entity: 1 });
eventWorkshopSchema.index({ event: 1 });
eventWorkshopSchema.index({ importID: 1 });

export default mongoose.model('EventWorkshop', eventWorkshopSchema);
