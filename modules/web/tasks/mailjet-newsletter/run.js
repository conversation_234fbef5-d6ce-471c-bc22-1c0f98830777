import fs from 'fs/promises';
import Handlebars from 'handlebars';
import { DateTime } from 'luxon';
import mjml2html from 'mjml';
import path from 'path';
import url from 'url';

import getArticleItemForSite from '#modules/articles/helpers/getArticleItemForSite.js';
import { getArticlesForNewsletter } from '#modules/articles/services/articlesServices.js';
import {
  logEvent,
  updateTaskStatus,
} from '#modules/tasks/services/taskRunService.js';
import Page from '#modules/web/models/Page.js';
import Site from '#modules/web/models/Site.js';
import { errorCodes, generateError } from '#utils/appError.js';
import { logError } from '#utils/logger.js';

import {
  createCampaignDraft,
  createCampaignDraftContent,
  sendCampaignDraft,
} from '../../services/providers/mailjet/mailjetServices.js';

const __dirname = url.fileURLToPath(new URL('.', import.meta.url));

async function getArticleDetailPage(task) {
  const { articleDetailPageId } = task.settings;

  try {
    return await Page.findById(articleDetailPageId);
  } catch (_err) {
    logError(
      'Mailjet article error',
      `Error generating article detail page url for pageId: ${articleDetailPageId}`
    );
  }
}

/**
 * Get articles for the campaign
 * @param {Object} site The site object
 * @param {Object} task The task object
 * @param {Object} taskRun The task run object
 * @returns {Promise<Object>} The articles for the campaign
 */
async function getCampaignArticles(site, task, taskRun) {
  try {
    const { items: articles, error } = await getArticlesForNewsletter({
      flagExceptions: task.settings.flagExceptions,
      flags: task.settings.flags,
      from: task.lastSync,
      siteId: site.id,
    });

    if (error) {
      return {
        error: generateError(
          'Mailjet articles error: Error fetching articles for campaign',
          errorCodes.TASK_ERROR,
          error.status || 400
        ),
      };
    }

    if (!articles || articles?.length === 0) {
      await logEvent(taskRun, 'No articles found to send.');

      await updateTaskStatus(taskRun, {
        status: 'cancelled',
        finishedAt: new Date(),
      });

      return { articles: [] };
    }

    await logEvent(taskRun, `Found ${articles.length} articles to send.`);

    const articleDetailPage = await getArticleDetailPage(task);

    const articlesForSite = await Promise.all(
      articles.map(
        async (item) =>
          await getArticleItemForSite({
            article: item,
            detailPage: articleDetailPage,
            includeSiteDomain: true,
            site,
          })
      )
    );

    const parsedArticles = articlesForSite.map(
      ({ location, organizations, publishedAt, ...article }) => ({
        ...article,
        publishedAt: DateTime.fromJSDate(publishedAt)
          .setLocale(site.language || 'en-US')
          .toLocaleString(DateTime.DATE_FULL),
        location: location.nameOverride || location.placeName,
        organizations: organizations
          ?.map((organization) => organization.name)
          .join(', '),
      })
    );
    return { articles: parsedArticles };
  } catch (_err) {
    return { error: { message: 'Error fetching articles for campaign' } };
  }
}

/**
 * Generate the campaign content using a Handlebars template
 * @param {Object} site The site object
 * @param {Object} task The task object
 * @param {Array} articles The articles to include in the campaign
 * @returns {Promise<Object>} The generated HTML content for the campaign
 */
async function generateCampaignContent(site, task, articles) {
  const { template } = task.settings;

  try {
    const mjml = await fs.readFile(
      path.join(__dirname, 'templates', `${template}.mjml`),
      { encoding: 'utf8' }
    );

    const mjmlTemplate = Handlebars.compile(mjml);

    const mjmlResult = mjmlTemplate({
      articles,
      dateSent: DateTime.now()
        .setLocale(site.language ?? 'en_US')
        .toLocaleString(DateTime.DATE_FULL),
      domain: site.domain,
      subject: task.settings.subject,
    });
    const { html } = mjml2html(mjmlResult);

    return { html };
  } catch (_err) {
    return {
      error: generateError(
        `Mailjet content error: Error generating content for campaign with template ${template}`,
        errorCodes.TASK_ERROR,
        400
      ),
    };
  }
}

/**
 * Create a campaign draft with the provided settings
 * @param {Object} options
 * @param {Object} options.site The site object
 * @returns {Promise<Object>} The created campaign draft
 */
async function contentToCampaignDraft({ content, draftId, site }) {
  return await createCampaignDraftContent({
    draftId,
    request: {
      'Headers': {},
      'Html-part': content,
    },
    site,
  });
}

/**
 * Run the Mailjet newsletter task
 *
 * This function is called by the task runner and executes the task logic.
 * It retrieves articles, generates the campaign content, creates a campaign draft,
 * and sends the campaign.
 *
 * @param task The task object containing the task settings and metadata
 * @param taskRun The task run object containing the task run details
 * @returns {Promise<Object>} The result of the task execution, including any errors
 */
export default async function runMailjetNewsletter(task, taskRun) {
  /**
   * EXECUTION PLAN
   *
   * 1. Retrive articles for newsletter
   * 2. Generate campaign email HTML
   *   - Generated from a predefined template saved in `./templates`
   *   - Use task settings to determine template
   * 3. Create Mailjet campaign draft
   *   - Use site settings to determine contact list
   *   - Use task settings to params
   * 4. Create the content for the campaign draft using the HTML from step 1
   * 5. Send the campaign
   */

  const { site: siteId } = task?.settings ?? {};

  if (!siteId) {
    return {
      error: new Error(
        `Error running Mailjet Campaign task (${task.id}): Task not properly configured`
      ),
    };
  }

  const site = await Site.findById(siteId);

  await updateTaskStatus(taskRun, {
    status: 'processing',
    startedAt: new Date(),
  });

  await logEvent(taskRun, '');
  await logEvent(
    taskRun,
    `Running Mailjet newsletter campaign for ${site.title}`
  );
  await logEvent(
    taskRun,
    '-----------------------------------------------------'
  );

  const { articles, error: articlesError } = await getCampaignArticles(
    site,
    task,
    taskRun
  );

  if (articlesError) {
    return {
      error: articlesError,
    };
  }

  if (articles.length > 0) {
    await logEvent(taskRun, `Generating campaign content`);
    const { html: content, error: campaignContentError } =
      await generateCampaignContent(site, task, articles);

    if (campaignContentError) {
      return {
        error: campaignContentError,
      };
    }

    await logEvent(taskRun, 'Creating campaign draft');

    const { campaignDraft, error: campaignDraftError } =
      await createCampaignDraft(site, task.settings);

    if (campaignDraftError) {
      return {
        error: campaignDraftError,
      };
    }

    const draftId = campaignDraft.ID;

    await logEvent(taskRun, 'Setting campaign draft content');
    const { error: campaignDraftContentError } = await contentToCampaignDraft({
      content,
      draftId,
      site,
      taskRun,
    });

    if (campaignDraftContentError) {
      return {
        error: campaignDraftError,
      };
    }

    await logEvent(taskRun, 'Sending campaign');

    // const { error: campaignSendError } = await sendCampaignDraft({
    //   draftId,
    //   site,
    // });

    const { error: campaignSendError } = await sendCampaignDraft({
      action: 'test',
      draftId,
      request: {
        Recipients: [
          {
            Name: 'Glen Somerville',
            Email: '<EMAIL>',
          },
        ],
      },
      site,
    });

    if (campaignSendError) {
      return {
        error: campaignSendError,
      };
    }
  }

  await logEvent(taskRun, '');
  await logEvent(taskRun, 'Mailjet newsletter campaign task finished');
  await logEvent(taskRun, '-----------------------------------------');
}
