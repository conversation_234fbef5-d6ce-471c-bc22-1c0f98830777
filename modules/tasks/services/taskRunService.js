import crypto from 'crypto';

import app from '#app';

import { ensureIdField } from '#utils/api/model/fields.js';
import { toObjectId } from '#utils/api/mongoose/id.js';
import getPaginationFilter from '#utils/api/pagination/filters.js';
import { errors } from '#utils/appError.js';
import {
  logDebug,
  logError,
  logInfo,
  logSuccess,
  logWarning,
} from '#utils/logger.js';

import TaskRun from '../models/TaskRun.js';
import { TASK_RUNS_NAMESPACE } from '../utils/sockets.js';

const logger = {
  info: logInfo,
  success: logSuccess,
  error: logError,
  warn: logWarning,
  debug: logDebug,
};

/**
 * Create a task run
 * @param {Object} options
 * @param {String} options.taskId - The task ID
 * @returns {Promise<Object>} - The task run
 */
export async function createTaskRun({ taskId }) {
  if (!taskId) {
    return {
      error: errors.params(['taskId']),
    };
  }

  const taskRun = await TaskRun.create({
    task: taskId,
  });

  return { taskRun };
}

/**
 * Get task run by ID
 * @param {String} taskRunId - The task run ID
 * @param {Object} options
 * @param {Boolean} [options.lean=true] - Whether to return a lean object
 * @returns {Promise<Object>} - The task run
 */
export async function getTaskRunById(taskRunId, { lean = true } = {}) {
  if (!taskRunId) {
    return {
      error: errors.params(['taskRunId']),
    };
  }

  const taskRunQuery = TaskRun.findById(toObjectId(taskRunId)).populate('task');

  if (lean) {
    taskRunQuery.lean();
  }

  const taskRun = await taskRunQuery;

  if (!taskRun) {
    return {
      error: errors.not_found('TaskRun'),
    };
  }

  return { taskRun: ensureIdField(taskRun) };
}

/**
 * Get the latest task run
 * @param {Object} options
 * @param {String} options.taskId - The task ID
 * @returns {Promise<Object>} - The task run
 */
export async function getLatestTaskRun({ taskId }) {
  if (!taskId) {
    return {
      error: errors.params(['taskId']),
    };
  }

  const taskRun = await TaskRun.findOne({
    task: toObjectId(taskId),
  }).sort({ createdAt: -1 });

  return { taskRun };
}

/**
 * Update a task run
 * @param {String} taskRunId - The task run ID
 * @param {Object} update - The update object
 * @returns {Promise<Object>} - The task run
 */
export async function updateTaskRun(taskRunId, update) {
  if (!taskRunId) {
    return {
      error: errors.params(['taskRunId']),
    };
  }

  const taskRun = await TaskRun.findByIdAndUpdate(
    toObjectId(taskRunId),
    update,
    { new: true }
  );

  if (!taskRun) {
    return {
      error: errors.not_found('TaskRun'),
    };
  }

  return { taskRun };
}

/**
 * Stop all task runs. This is used when the server is shutting down
 * @returns {Promise<void>}
 */
export async function stopAllTaskRuns() {
  logInfo('🗙 Cancelling all pending task runs...');

  await TaskRun.updateMany(
    { $or: [{ status: 'pending' }] },
    { $set: { status: 'cancelled' } }
  );

  logInfo('All pending task runs stopped.');
}

/**
 * Get all pending task runs for a task
 * @param {Object} options
 * @param {Number} [options.limit=3] - The maximum number of tasks to return
 * @param {Number} [options.page] - The page number *
 * @param {String} options.taskId - The task ID
 * @param {Number} [options.skip=0] - The number of tasks to skip
 * @param {pending|processing|completed|cancelled|failed} [options.status] - The status of the task runs
 * @returns {Promise<Object>} - The task runs
 */
export async function getTaskRuns({
  limit = 3,
  page,
  taskId,
  skip = 0,
  status,
}) {
  if (!taskId) {
    return {
      error: errors.params(['taskId']),
    };
  }

  const paginationFilter = getPaginationFilter({
    limit,
    page,
    skip,
  });

  const taskRuns = await TaskRun.find({
    task: toObjectId(taskId),
    ...(status ? { status } : {}),
  })
    .sort({ startedAt: -1 })
    .skip(paginationFilter.skip)
    .limit(paginationFilter.limit);

  return { taskRuns };
}

/**
 * Log an event for a task run
 * @param {Object} taskRun - The task run object
 * @param {string|Array<string>} message - The message to log
 * @param {'info'|'success'|'warn'|'error'|'debug'} [level='info'] - The log level (info, success, warn, error, debug)
 * @param {boolean} [verbose=false] - Whether to log in verbose mode
 * @returns {Promise<void>}
 */
export async function logEvent(
  taskRun,
  message,
  level = 'info',
  verbose = false
) {
  if (!taskRun || !taskRun._id) {
    logError('Task run is required and must have an _id');
    return;
  }

  const taskRunId = taskRun._id.toString();

  // Track sent logs to avoid duplicates
  const sentLogIds = new Set(taskRun?.details?.logs?.map((log) => log.id));

  // Create a per-taskRun save queue to prevent parallel save() calls
  let saveQueue = Promise.resolve();

  const logEntry = {
    id: crypto.randomUUID(),
    timestamp: new Date(),
    level,
    message: Array.isArray(message) ? message.join(', ') : message,
  };

  const io = app.get('socketio');

  if (verbose) {
    const log = logger[level] || logger.info;
    log(message);
  }

  // Ensure nested structure exists
  taskRun.details = taskRun.details || {};
  taskRun.details.logs = taskRun.details.logs || [];
  taskRun.details.logs.push(logEntry);
  taskRun.markModified('details.logs');

  // Serialize saves to avoid "Can't save() the same doc multiple times in parallel" errors
  saveQueue = saveQueue.then(async () => {
    try {
      await taskRun.save();
    } catch (err) {
      logError(`Failed to save log: ${err.message}`);
    }
  });

  // Await the queued save to preserve previous behavior (persist before emit completes)
  await saveQueue;

  if (!sentLogIds.has(logEntry.id)) {
    sentLogIds.add(logEntry.id);
    io.of(`/${TASK_RUNS_NAMESPACE}`).to(taskRunId).emit('log', logEntry); // Emit to task room
  }
}

/**
 * Fail a task run with a message and optional error details
 * @param {Object} taskRun - The task run object
 * @param {string|Array<string>} message - The failure message
 * @param {Object} [errorDetails] - Additional error details to save in the task run
 * @returns {Promise<void>}
 */
export async function failTaskRun(taskRun, message, errorDetails) {
  if (!taskRun || !taskRun._id) {
    logError('Task run is required and must have an _id');
    return;
  }

  await logEvent(taskRun, message, 'error');

  if (errorDetails) {
    taskRun.details = { ...taskRun.details, ...errorDetails };
  }

  await taskRun.save();

  updateTaskStatus(taskRun, {
    status: 'failed',
    finishedAt: new Date(),
  });
}

/**
 * Update task run status
 * @param {Object} taskRun - The task run
 * @param {Object} statusUpdate - The status update
 * @returns {void}
 */
export async function updateTaskStatus(taskRun, statusUpdate) {
  if (!taskRun || !taskRun._id) {
    logError('Task run ID is required to update status');
    return;
  }

  const { status, finishedAt, startedAt } = statusUpdate;

  taskRun.status = status;
  if (startedAt) {
    taskRun.startedAt = startedAt;
  }
  if (finishedAt) {
    taskRun.finishedAt = finishedAt;
  }

  await taskRun.save();

  const io = app.get('socketio');
  io.of(`/${TASK_RUNS_NAMESPACE}`)
    .to(taskRun?._id.toString())
    .emit('taskRunUpdate', statusUpdate);
}

export default {
  createTaskRun,
  getTaskRunById,
  getLatestTaskRun,
  updateTaskRun,
  stopAllTaskRuns,
  getTaskRuns,
};
