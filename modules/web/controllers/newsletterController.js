import tasksService from '#modules/tasks/services/tasksService.js';
import factory from '#utils/handlerFactory.js';
import Task from '#modules/tasks/models/Task.js';
import { errors } from '#utils/appError.js';

export const getCampaigns = async (req, res) => {
  const { entity, params } = req;
  const { siteId } = params;

  const { tasks, error } = await tasksService.getTasks({
    entityId: entity._id,
    filterByEntity: true,
    siteId,
    // NOTE: Using a static type for now. Should switch to dynamic types later when more providers are supported.
    type: 'mailjet-newsletter',
  });

  if (error) {
    throw error;
  }

  res.status(200).json({ items: tasks, count: tasks.length });
};

export const getCampaign = async (req, res) => {
  const { entity, params } = req;
  const { campaignId } = params;

  const { task, error } = await tasksService.getTaskById(campaignId, {
    entityId: entity._id,
    filterByEntity: true,
  });

  if (error) {
    throw error;
  }

  res.status(200).json(task);
};

export const createCampaign = async (req, res) => {
  const { body, entity, params } = req;
  const { siteId } = params;

  const task = await tasksService.createTask(entity._id, {
    ...body,
    settings: {
      ...body.settings,
      site: siteId,
    },
    // NOTE: Using a static type for now. Should switch to dynamic types later when more providers are supported.
    type: 'mailjet-newsletter',
  });

  res.status(200).json(task);
};

export const updateCampaign = async (req, res) => {
  const { body, entity, params } = req;
  const { siteId, campaignId } = params;

  const { task, error } = await tasksService.updateTask(
    campaignId,
    entity._id,
    {
      ...body,
      settings: {
        ...body.settings,
        site: siteId,
      },
      // NOTE: Using a static type for now. Should switch to dynamic types later when more providers are supported.
      type: 'mailjet-newsletter',
    },
    req.body
  );

  if (error) {
    throw error;
  }

  res.status(200).json(task);
};

export const enableCampaign = async (req, res) => {
  const data = await factory.enableOne(Task, req, {
    paramId: 'campaignId',
  });

  res.status(200).json(data);
};

export const disableCampaign = async (req, res) => {
  const data = await factory.disableOne(Task, req, {
    paramId: 'campaignId',
  });

  res.status(200).json(data);
};

export const deleteCampaign = async (req, res) => {
  const data = await factory.deleteOne(Task, req, {
    paramId: 'campaignId',
  });
  res.status(200).json(data);
};

export const runCampaign = async (req, res) => {
  const { params } = req;
  const { siteId, campaignId } = params;

  if (!siteId || !campaignId) {
    throw errors.params(['siteId', 'campaignId']);
  }

  const { taskRun, type, error } = await tasksService.executeTask(campaignId);

  if (error) {
    throw error;
  }

  res.status(200).json({
    taskRun,
    type,
  });
};
