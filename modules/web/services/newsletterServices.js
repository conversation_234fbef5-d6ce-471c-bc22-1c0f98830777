import { providers } from './providers/index.js';

export function encryptNewsletterSettings({ newsletter, prevNewsletter }) {
  if (providers[newsletter?.provider || '']?.encrypt) {
    return providers[newsletter.provider].encrypt(newsletter, prevNewsletter);
  }
  return newsletter;
}

export function decryptNewsletterSettings(newsletter = {}) {
  if (providers[newsletter?.provider || '']?.decrypt) {
    return providers[newsletter.provider].decrypt(newsletter);
  }
  return newsletter;
}
