import { slugify } from '#utils/strings.js';
import {
  logEvent,
  updateTaskStatus,
} from '#modules/tasks/services/taskRunService.js';

import getEntityFromField from './getEntityFromField.js';
import countries from '../config/countries.js';
import { languagesMap } from '../config/languages.js';

import {
  ORGMAST_API_URL,
  IMPORT_BATCH_SIZE,
  DRY_RUN,
  LOG_PREFIX,
  VERBOSE,
} from '../config/constants.js';

export default async function importEntities(taskRun, lastSync = 0) {
  const stats = {
    importCount: 0,
    skippedCount: 0,
    updateCount: 0,
    deleteCount: 0,
  };

  await updateTaskStatus(taskRun, {
    status: 'processing',
    startedAt: new Date(),
  });

  // Generate countries map for easy access
  const countriesMap = countries.reduce((acc, country) => {
    const contryNameSlug = slugify(country.name);
    acc[contryNameSlug] = country;
    return acc;
  }, {});

  if (DRY_RUN) {
    await logEvent(
      taskRun,
      [
        LOG_PREFIX,
        '(!) Dry-run mode enabled. No changes will be made to the database.',
      ],
      'warn'
    );
  }

  if (VERBOSE) {
    await logEvent(
      taskRun,
      [LOG_PREFIX, '(!) Verbose mode enabled. More details will be logged.'],
      'warn'
    );
  }

  try {
    await logEvent(taskRun, [
      LOG_PREFIX,
      'Fetching administrative fields and yearbook entities...',
    ]);

    const endpointUrls = [
      '/OMAdmField', // Administrative fields
      '/OMEntityYBOnly', // Yearbook entities
    ];

    const requests = endpointUrls.map((url) =>
      fetch(`${ORGMAST_API_URL}/${url}`)
    );
    const responses = await Promise.all(requests);
    const errors = responses.filter((response) => !response.ok);

    if (errors.length > 0) {
      throw errors.map((response) => Error(response.statusText));
    }

    const json = responses.map((response) => response.json());
    const [fields, yearbookEntities] = await Promise.all(json);

    // Converts fields array into a map object
    await logEvent(taskRun, [LOG_PREFIX, 'Mapping administrative fields...']);
    const fieldsMap = fields.reduce((acc, field) => {
      const { Name, EntityID, ParentEntityID, Active, AdmFieldID, ParentID } =
        field || {};

      if (EntityID) {
        acc[EntityID] = {
          Name, // Name of the entity
          EntityID, // ID of the entity (like "10010" for GC)
          ParentEntityID, // ID of the parent entity (like "10010" for GC)
          ParentID, // Code of the parent entity (like "GC", "NAD", "SAD", etc.)
          Active, // indicates if the entity is active or not
          AdmFieldID, // Code of the entity (like "GC" for GC)
        };
      }

      return acc;
    }, {});

    // Generate a map of fields code to entity ID for easy access when importing entities.
    // Yearbook entities don't have a reference to their parents as EntityID (like "10010"), but as AdmFieldID (like "GC"), so we need this to easy access the parent entity ID.
    const fieldsCodeMap = Object.keys(fieldsMap).reduce((acc, field) => {
      const { AdmFieldID, EntityID } = fieldsMap[field] || {};

      if (EntityID && AdmFieldID) {
        acc[AdmFieldID] = EntityID;
      }

      return acc;
    }, {});

    await logEvent(taskRun, [LOG_PREFIX, 'Mapping yearbook entities...']);
    for (const entity of yearbookEntities) {
      // Look if the entity is already in the fieldsMap
      const adminField = fieldsMap[entity.EntityID];

      // Use yearbook entity id as key for both admin fields and yearbook entities
      fieldsMap[entity.EntityID] = {
        ...(adminField || {}), // Add admin field data if available
        ...entity, // Use yearbook entity data
        Code: adminField ? adminField.AdmFieldID : undefined, // Use admin field code for admin fields, or undefined for yearbook entities // ASK: What is the code for yearbook entities?
        ParentCode: adminField
          ? adminField.ParentID // for admin fields, use the parent ID as the parent field code
          : entity.AdmFieldID, // in Yearbook entities, AdmFieldID is the parent, and can be used as a fallback to get the parent entity.
        Active: adminField ? adminField.Active : entity.Active, // Use admin field Active if available
        sync: false, // Initialize sync flag to track already imported entities
      };
    }

    let count = 0;
    let batch = 0;
    const missingLanguages = {};
    const fieldsIds = Object.keys(fieldsMap || {});
    const totalEntities = fieldsIds.length;
    const batches = Math.ceil(totalEntities / IMPORT_BATCH_SIZE);

    await logEvent(taskRun, [LOG_PREFIX, 'Importing entities...']);

    for (const fieldId of fieldsIds) {
      const field = fieldsMap[fieldId];

      // Skip record if entity ID is missing
      if (!field.EntityID) {
        await logEvent(
          taskRun,
          [LOG_PREFIX, 'EntityID missing for field', field.toString()],
          'error'
        );
        continue;
      }

      // If language is not in the languagesMap, add it to the languages object
      if (field.Language && !languagesMap[field.Language]) {
        if (!missingLanguages[field.Language]) {
          missingLanguages[field.Language] = {
            code: '',
            count: 0,
            entities: [],
          };
        }

        missingLanguages[field.Language].count += 1;
        missingLanguages[field.Language].entities.push(fieldId);
      }

      // Import entity from field
      await getEntityFromField({
        fieldsMap,
        fieldsCodeMap,
        id: fieldId,
        stats,
        countriesMap,
        lastSync,
      });

      // Log batch progress every IMPORT_BATCH_SIZE entities
      if (count % IMPORT_BATCH_SIZE === 0) {
        batch += 1;

        await logEvent(taskRun, [
          LOG_PREFIX,
          `Importing batch ${batch} of ${batches} (from ${count + 1} to ${count + IMPORT_BATCH_SIZE} of ${totalEntities} entities)`,
        ]);
      }

      count += 1;
    }

    // Log missing languages
    if (Object.keys(missingLanguages).length > 0) {
      await logEvent(
        taskRun,
        [LOG_PREFIX, 'Missing languages:', missingLanguages.toString()],
        'warn'
      );
    }

    await logEvent(taskRun, [
      LOG_PREFIX,
      `${stats.importCount} entities imported`,
    ]);
    await logEvent(taskRun, [
      LOG_PREFIX,
      `${stats.updateCount} entities updated`,
    ]);
    await logEvent(taskRun, [
      LOG_PREFIX,
      `${stats.deleteCount} entities deleted`,
    ]);
  } catch (error) {
    await logEvent(
      taskRun,
      [LOG_PREFIX, 'Error when trying to import entities:', error.toString()],
      'error'
    );
  }

  return stats;
}
