{"name": "web-engine-core-api", "version": "1.0.0", "description": "Adventist Web Engine Core API", "private": true, "main": "server.js", "type": "module", "engines": {"node": ">=22.x <23"}, "imports": {"#app": "./app.js", "#middleware/*": "./middleware/*", "#modules/*": "./modules/*", "#scripts/*": "./scripts/*", "#templates/*": "./templates/*", "#utils/*": "./utils/*"}, "scripts": {"dev": "nodemon server --ignore temp", "dev:prod": "dotenv -e .env.prod nodemon server", "dev:local-staging": "dotenv -e .env.local-staging nodemon server", "dev:local-prod": "dotenv -e .env.local-prod nodemon server", "dev:local-prod-scalingo": "dotenv -e .env.local-prod-scalingo nodemon server", "start": "node server.js", "lint": "eslint .", "test": "vitest"}, "dependencies": {"@aws-sdk/client-s3": "^3.855.0", "@aws-sdk/lib-storage": "^3.855.0", "@aws-sdk/s3-request-presigner": "^3.855.0", "@exortek/express-mongo-sanitize": "^1.1.0", "@maizzle/framework": "^5.2.2", "@supercharge/request-ip": "^1.2.0", "@tiptap/core": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/html": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@vimeo/vimeo": "^3.0.3", "basic-ftp": "^5.0.5", "bcryptjs": "^3.0.2", "blurhash": "^2.0.5", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "croner": "^9.1.0", "deepmerge": "^4.3.1", "dom-serializer": "^2.0.0", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "fast-xml-parser": "^5.2.5", "googleapis": "^154.1.0", "handlebars": "^4.7.8", "he": "^1.2.0", "helmet": "^8.1.0", "html-to-text": "^9.0.5", "htmlparser2": "^10.0.0", "http-proxy": "^1.18.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jssha": "^3.3.1", "ky": "^1.8.2", "lodash": "^4.17.21", "luxon": "^3.7.1", "meilisearch": "^0.51.0", "mime-types": "^3.0.1", "mjml": "^4.15.3", "mongoose": "^8.16.5", "morgan": "^1.10.1", "multer": "^2.0.2", "node-mailjet": "^6.0.9", "nodemailer": "^7.0.5", "otplib": "^12.0.1", "sharp": "^0.34.3", "slate": "^0.117.2", "socket.io": "^4.8.1", "source-map-js": "^1.2.0", "string-strip-html": "^13.4.13", "tailwindcss-preset-email": "^1.4.0", "ua-parser-js": "^2.0.4", "validator": "^13.15.15", "vimeo": "^2.3.1", "vitest": "^3.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.32.0", "dotenv-cli": "^9.0.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-n": "^17.21.3", "globals": "^16.3.0", "nodemon": "^3.1.10", "prettier": "^3.6.2"}}