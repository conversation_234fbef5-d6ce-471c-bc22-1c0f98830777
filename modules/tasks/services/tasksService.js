import { isValidObjectId, toObjectId } from '#utils/api/mongoose/id.js';
import { errors } from '#utils/appError.js';
import { isFunction } from '#utils/types.js';
import { ensureIdField } from '#utils/api/model/fields.js';

import app from '../../../app.js';
import Task from '../models/Task.js';
import {
  createTaskRun,
  failTaskRun,
  logEvent,
  updateTaskStatus,
} from './taskRunService.js';

/**
 * Populate task with additional information
 * @param {Object} task The task object to populate
 * @returns {Object} The populated task object
 */
async function populateTask(task) {
  const populatedTask = { ...task };
  const availableTasks = app.get('tasks');
  const registeredTasks = app.get('registeredTasks') ?? {};

  const taskSettings = availableTasks[task?.type];

  // If the task is not available, skip it
  if (!taskSettings) {
    return { task };
  }

  try {
    const { module, title, record, context } = taskSettings?.getMeta
      ? await taskSettings.getMeta({ task })
      : {};
    populatedTask.record = record;
    populatedTask.module = module;
    populatedTask.title = title;
    populatedTask.meta = { context };

    if (registeredTasks?.[task._id]) {
      populatedTask.meta = {
        ...(context ? { context } : {}),
        job: {
          nextRun: registeredTasks[task._id].nextRun(),
        },
      };
    }

    return { task: ensureIdField(populatedTask) };
  } catch (error) {
    return { error };
  }
}

/**
 * Get tasks from the database
 * @param {Object} options Options for filtering tasks
 * @param {boolean} options.filterByEntity Whether to filter tasks by entity
 * @param {String} options.entityId The ID of the entity to filter the tasks by
 * @param {string} options.siteId The ID of the site to filter the tasks by
 * @returns {Object} The tasks and any errors that occurred
 */
async function getTasks({
  filterByEntity = true,
  entityId,
  siteId = null,
  type = null,
} = {}) {
  const conditions = {};

  if (filterByEntity) {
    conditions.entity = toObjectId(entityId);
  }

  if (siteId) {
    conditions['settings.site'] = siteId;
  }
  if (type) {
    conditions.type = type;
  }

  const tasks = await Task.find({
    deleted: false,
    ...conditions,
  }).lean();

  if (!tasks) {
    return { error: errors.not_found('Tasks') };
  }

  // Populate tasks
  const populatedTasks = [];
  const populatedTaskErrors = [];

  for (const task of tasks) {
    const { task: populatedTask, error: populatedTaskError } =
      await populateTask(task);

    if (populatedTaskError) {
      populatedTaskErrors.push(populatedTaskError);
      continue;
    }

    populatedTasks.push(populatedTask);
  }

  return { tasks: populatedTasks, errors: populatedTaskErrors };
}

/**
 * Get a task by its ID
 * @param {String} taskId The ID of the task to retrieve
 * @param {Object} options Options for filtering tasks
 * @param {boolean} options.filterByEntity Whether to filter tasks by entity
 * @param {String} options.entityId The ID of the entity to filter the tasks by
 * @returns {Object} The task and any errors that occurred
 */
async function getTaskById(taskId, { filterByEntity = true, entityId } = {}) {
  const validId = isValidObjectId(taskId);

  if (!validId) {
    return { error: errors.not_found() };
  }

  const task = await Task.findOne({
    _id: toObjectId(taskId),
    deleted: false,
    ...(filterByEntity ? { entity: entityId } : {}),
  }).lean();

  if (!task) {
    return { error: errors.not_found('Task', taskId) };
  }

  const { task: populatedTask, error: populatedTaskError } =
    await populateTask(task);

  if (populatedTaskError) {
    return { error: populatedTaskError };
  }

  return { task: populatedTask };
}

/**
 * Get scheduled tasks from the database
 * @returns {Array} The scheduled tasks
 */
async function getScheduledTasks() {
  return Task.find({
    'deleted': false,
    'enabled': true,
    'settings.schedule': { $exists: true },
    'settings.scheduled': true,
  });
}

/**
 * Execute a task by its ID
 * @param {String} taskId The ID of the task to execute
 * @returns {Object} The results of the task execution
 * @throws {Error} If the task type doesn't exist or if an error occurs during execution
 */
async function executeTask(taskId) {
  const task = await Task.findOne({
    _id: taskId,
    deleted: false,
  });

  if (!task) {
    return { error: errors.not_found('Task', taskId) };
  }

  const { type } = task;

  const availableTasks = app.get('tasks');
  const taskSettings = availableTasks[type];

  if (!taskSettings?.run) {
    return { error: errors.task_error("Task type doesn't exist") };
  }

  const { run, getTitle } = taskSettings;

  const taskTitle = isFunction(getTitle) ? await getTitle(task) : type;

  const { taskRun, error: taskRunError } = await createTaskRun({ taskId });

  if (taskRunError) {
    return { error: taskRunError };
  }

  // Trigger re-indexing, do not await the result
  runTask({ run, task, taskRun, taskTitle });

  return { taskRun, type };
}

async function runTask({ run, task, taskRun, taskTitle }) {
  try {
    await logEvent(taskRun, '-----------------------------------------------');
    await logEvent(taskRun, `Starting task '${taskTitle}'`);

    // Run task
    const results = await run(task, taskRun);

    // Update last sync date with current date
    await Task.updateOne({ _id: task.id }, { lastSync: Date.now() });

    // In case of an error, log it and update task run status
    if (results?.error) {
      await failTaskRun(taskRun, results.error.message, results.error);

      return { error: results.error };
    }

    await logEvent(taskRun, '');
    await logEvent(taskRun, 'Done');
    await logEvent(taskRun, '');

    if (taskRun.status !== 'cancelled') {
      await updateTaskStatus(taskRun, {
        status: 'completed',
        finishedAt: new Date(),
      });
    }
  } catch (error) {
    const errorMessage = `Error running task '${taskTitle}'`;
    await failTaskRun(taskRun, errorMessage, error);
  }
}

/**
 * Create a new task
 * @param {String} entityId The ID of the entity to create the task for
 * @param {Object} taskData The data for the new task
 * @returns {Object} The created task
 */
async function createTask(entityId, taskData) {
  const task = await Task.create({
    ...taskData,
    entity: entityId,
  });

  return task;
}

/**
 * Update an existing task
 * @param {String} taskId The ID of the task to update
 * @param {String} entityId The ID of the entity to update the task for
 * @param {Object} taskData The data to update the task with
 * @returns {Object} The updated task
 */
async function updateTask(taskId, entityId, taskData) {
  const { task, error: taskError } = await getTaskById(taskId, {
    entityId,
  });

  if (taskError) {
    return { error: taskError };
  }

  // Update task's data
  const updatedTask = await Task.findByIdAndUpdate(
    task._id,
    {
      ...taskData,
      entity: task.entity, // prevents moving a task to different entity
    },
    {
      new: true,
      runValidators: true,
    }
  );

  return { task: updatedTask };
}

export default {
  getTasks,
  getTaskById,
  getScheduledTasks,
  executeTask,
  createTask,
  updateTask,
};
