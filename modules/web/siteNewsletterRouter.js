import express from 'express';

import { isRouteEnabled } from '#modules/feature-flags/middleware/isRouteEnabled.js';
import { logRequest } from '#modules/logs/middlewares/logMiddleware.js';
import { restrictTo } from '#modules/users/controllers/authController.js';

import {
  createCampaign,
  deleteCampaign,
  disableCampaign,
  enableCampaign,
  getCampaign,
  getCampaigns,
  runCampaign,
  updateCampaign,
} from './controllers/newsletterController.js';

const siteNewsletterRouter = express.Router({ mergeParams: true });

siteNewsletterRouter
  .route('/campaigns')
  .get(
    isRouteEnabled('site-newsletter'),
    restrictTo({
      module: 'newsletter',
      permissions: ['read'],
      paramId: 'siteId',
    }),
    getCampaigns
  )
  .post(
    isRouteEnabled('site-newsletter'),
    restrictTo({
      module: 'newsletter',
      permissions: ['create'],
      paramId: 'siteId',
    }),
    logRequest({ module: 'web', action: 'CREATE_CAMPAIGN' }),
    createCampaign
  );

siteNewsletterRouter
  .route('/campaigns/:campaignId')
  .get(
    isRouteEnabled('site-newsletter'),
    restrictTo({
      module: 'newsletter',
      permissions: ['read'],
      paramId: 'siteId',
    }),
    getCampaign
  )
  .patch(
    isRouteEnabled('site-newsletter'),
    restrictTo({
      module: 'newsletter',
      permissions: ['update'],
      paramId: 'siteId',
    }),
    logRequest({ module: 'web', action: 'UPDATE_CAMPAIGN' }),
    updateCampaign
  )
  .delete(
    isRouteEnabled('site-newsletter'),
    restrictTo({
      module: 'newsletter',
      permissions: ['delete'],
      paramId: 'siteId',
    }),
    logRequest({ module: 'web', action: 'DELETE_CAMPAIGN' }),
    deleteCampaign
  );

siteNewsletterRouter.route('/campaigns/:campaignId/enable').patch(
  isRouteEnabled('site-newsletter'),
  restrictTo({
    module: 'newsletter',
    permissions: ['update'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'ENABLE_CAMPAIGN' }),
  enableCampaign
);

siteNewsletterRouter.route('/campaigns/:campaignId/disable').patch(
  isRouteEnabled('site-newsletter'),
  restrictTo({
    module: 'newsletter',
    permissions: ['update'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'DISABLE_CAMPAIGN' }),
  disableCampaign
);

siteNewsletterRouter.route('/campaigns/:campaignId/run').post(
  isRouteEnabled('site-newsletter'),
  restrictTo({
    module: 'newsletter',
    permissions: ['run'],
    paramId: 'siteId',
  }),
  logRequest({ module: 'web', action: 'RUN_CAMPAIGN' }),
  runCampaign
);

export default siteNewsletterRouter;
