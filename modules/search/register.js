import searchRouter from './routes.js';
import searchTasks from './tasks/index.js';
import {
  SEARCH_INDEX_TASKS_NAMESPACE,
  registerSearchIndexTasksSocket,
} from './utils/sockets.js';

export default function search() {
  return {
    routes: {
      '/search': searchRouter,
    },
    sockets: {
      [SEARCH_INDEX_TASKS_NAMESPACE]: registerSearchIndexTasksSocket,
    },
    tasks: searchTasks,
  };
}
