import mongoose from 'mongoose';

import SchemaFactory from '#utils/schemaFactory.js';

const taskRunSchema = SchemaFactory({
  task: {
    type: mongoose.Types.ObjectId,
    ref: 'Task',
  },

  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'cancelled', 'failed'],
    default: 'pending',
  },

  startedAt: {
    type: Date,
  },

  // The time the task run was finished, whether it succeeded or failed
  finishedAt: {
    type: Date,
  },

  duration: {
    type: Number,
  },

  details: {
    type: mongoose.SchemaTypes.Mixed,
    default: {},
  },
});

// Indexes
taskRunSchema.index({ task: 1 });
taskRunSchema.index({ status: 1 });
taskRunSchema.index({ startedAt: 1 });
taskRunSchema.index({ finishedAt: 1 });

// Export the model
export default mongoose.model('TaskRun', taskRunSchema);
