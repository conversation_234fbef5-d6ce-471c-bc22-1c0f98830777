import jetstream from '#utils/jetstream.js';
import youtube from '#utils/youtube.js';
import vimeo from '#utils/vimeo.js';

import Channel from '#modules/media-library/models/Channel.js';
import Episode from '#modules/media-library/models/Episode.js';
import {
  logEvent,
  updateTaskStatus,
} from '#modules/tasks/services/taskRunService.js';

export default async function runStatisticsUpdater(task, taskRun) {
  // Get task settings
  const {
    channel: channelId,
    updateYouTubeViews = true,
    updateJetstreamViews = true,
    updateVimeoViews = true,
    debug = false,
  } = task.settings;

  await updateTaskStatus(taskRun, {
    status: 'processing',
    startedAt: new Date(),
  });

  // Performance
  // - https://dev.to/karataev/handling-a-lot-of-requests-in-javascript-with-promises-1kbb
  // - https://rakshanshetty.in/nodejs-http-keep-alive/

  const mediaLinksFilter = [];
  if (updateYouTubeViews) {
    mediaLinksFilter.push({ 'mediaLinks.youtube': { $ne: null } });
  }
  if (updateJetstreamViews) {
    mediaLinksFilter.push({ 'mediaLinks.jetstream': { $ne: null } });
  }
  if (updateVimeoViews) {
    mediaLinksFilter.push({ 'mediaLinks.vimeo': { $ne: null } });
  }

  // Get episodes
  const episodes = await Episode.find({
    // - for the channel configured in the task
    channel: channelId,
    // - not deleted
    deleted: false,
    // - enabled
    enabled: true,
    // - that is video on demand
    videoOnDemand: true,
    $expr: {
      $and: [
        // - that has started (or has no start date)
        { $lte: [{ $ifNull: ['$videoOnDemandStartsAt', '$$NOW'] }, '$$NOW'] },
        // - and has not ended (or has no end date)
        { $gte: [{ $ifNull: ['$videoOnDemandEndsAt', '$$NOW'] }, '$$NOW'] },
      ],
    },
    // Include media links filters to only get episodes with media links for the platforms we want to update
    ...(mediaLinksFilter && { $or: mediaLinksFilter }),
  })
    .select('title mediaLinks views')
    .sort('-createdAt')
    .limit(10000); // Limit to 10,000 episodes // ASK: @sdh-olivier Isn't this too much? Can it be done in chunks?

  // Init vimeo client
  // ASK: @sdh-olivier Do we need to initialize the Vimeo client even if the channel does not have a Vimeo configuration?
  const channel = await Channel.findById(channelId, 'vimeo title jetstream');
  vimeo.initClient(channel);

  await logEvent(taskRun, channel.title.toUpperCase());
  await logEvent(taskRun, '-------------------------------');
  await logEvent(taskRun, `${episodes.length} episodes:`);

  const serverProblems = {
    youtube: false,
    jetstream: false,
    vimeo: false,
  };

  let jetstreamVideoEventsAggregate = null;

  if (updateJetstreamViews && !serverProblems.jetstream) {
    jetstreamVideoEventsAggregate = await jetstream.getVideoEventsAggregate(
      channel.jetstream?.organizationId
    );
  }

  // Loop through episodes
  let i = 1;
  for (const episode of episodes) {
    // episodes.forEach(async (episode) => {
    const { mediaLinks } = episode;

    // Only log episode title if debug is enabled
    if (debug) {
      await logEvent(taskRun, `- ${episode.title}`);
    }

    const youtubeViews = episode.views?.youtube || 0;
    const jetstreamViews = episode.views?.jetstream || 0;
    const vimeoViews = episode.views?.vimeo || 0;
    const episodeTotalViews = youtubeViews + jetstreamViews + vimeoViews;

    const views = {
      youtube: youtubeViews,
      jetstream: jetstreamViews,
      vimeo: vimeoViews,
    };

    // Get views for YouTube
    if (updateYouTubeViews && !serverProblems.youtube) {
      const youtubeId = mediaLinks.youtube?.link;
      if (youtubeId) {
        try {
          const youtubeVideo = await youtube.getVideo(youtubeId);
          if (youtubeVideo) {
            views.youtube = Number.parseInt(
              youtubeVideo.statistics.viewCount,
              10
            );
          }
        } catch (error) {
          serverProblems.youtube = true;
          await logEvent(
            taskRun,
            `Server error getting YouTube video (${youtubeId}): ${error.toJSON().toString()}`,
            'error'
          );
        }
      }
    }

    // Get views for Jetstream
    if (updateJetstreamViews && !serverProblems.jetstream) {
      const jetstreamId = jetstream.getJetstreamId(mediaLinks.jetstream?.link);
      if (jetstreamId) {
        try {
          // await logEvent(taskRun, `    >> ${jetstreamId}`);
          const jetstreamAnalytics = jetstreamVideoEventsAggregate?.find(
            (videoEvent) => videoEvent.id === jetstreamId
          ) ?? { views: 0 };
          // await logEvent(taskRun, `    >> ${jetstreamAnalytics.views}`);
          if (jetstreamAnalytics?.views) {
            views.jetstream = Number.parseInt(jetstreamAnalytics.views, 10);
          }
        } catch (error) {
          serverProblems.jetstream = true;
          await logEvent(
            taskRun,
            `Server error getting jetstream analytics (${jetstreamId}): ${error.toJSON().toString()}`,
            'error'
          );
        }
      }
    }

    // Get views for Vimeo
    if (updateVimeoViews && !serverProblems.vimeo) {
      const vimeoId = mediaLinks.vimeo?.link;
      if (vimeoId) {
        try {
          const vimeoVideo = await vimeo.getVideo(vimeoId);
          if (vimeoVideo) views.vimeo = vimeoVideo.stats.plays;
        } catch (error) {
          serverProblems.vimeo = true;
          await logEvent(
            taskRun,
            `Server error getting vimeo video (${vimeoId}): ${error.toJSON().toString()}`,
            'error'
          );
        }
      }
    }

    if (episodeTotalViews !== views.youtube + views.jetstream + views.vimeo) {
      await Episode.updateOne({ _id: episode.id }, { views });
    }

    // Sleep 0.12 seconds to avoid too many requests (less than 10 requests per second)
    // sleep(120);

    // Print progress
    if (i % 100 === 0) {
      await logEvent(taskRun, i.toString());
    }

    i += 1;
  }

  return {};
}

// function splitToChunks(episodes, chunkSize) {
//   const result = [];
//   for (let i = 0; i < episodes.length; i += chunkSize) {
//     result.push(episodes.slice(i, i + chunkSize));
//   }
//   return result;
// }

// function updateByChunks(allEpisodes, fn, chunkSize = 50) {
//   let result = [];
//   const episodes = splitToChunks(items, chunkSize);
//   // usw.
// }

// const sleep = (ms) => {
//   const end = new Date().getTime() + ms;
//   while (new Date().getTime() < end) {}
// };
