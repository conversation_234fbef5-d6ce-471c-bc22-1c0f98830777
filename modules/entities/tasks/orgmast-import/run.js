import { toUnixDate } from '#utils/dates.js';
import importEntities from './helpers/importEntities.js';

export default async function runOrgmastImport(task, taskRun) {
  // Get task last sync date, or default to 0
  const lastSync = task.lastSync ? toUnixDate(task.lastSync) : 0;
  // const lastSync = 0; // For full import...

  const entityResults = await importEntities(taskRun, lastSync);
  return entityResults;
}
