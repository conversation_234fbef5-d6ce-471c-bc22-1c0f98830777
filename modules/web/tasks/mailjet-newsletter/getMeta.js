import Site from '../../models/Site.js';
import {
  getAllContactLists,
  getAllSenders,
} from '../../services/providers/mailjet/mailjetServices.js';

export default async function getMailjetNewsletterMeta({ task }) {
  if (!task) {
    throw new Error('Task is required');
  }

  const { settings } = task;

  const site = await Site.findById(settings.site);

  return {
    module: 'web',
    name: 'mailjet-newsletter',
    title: site?.name || site?.title,
    context: await getMailjetMetaForSite(site),
  };
}

const templates = [
  {
    value: 'adventisten-de-weekly',
    label: 'Adentisten.de weekly newsletter',
  },
  {
    value: 'frauen-adventisten-de',
    label: 'Adentisten.de Frauen newsletter',
  },
  {
    value: 'eud-newsletter',
    label: 'EUD ALPS Newsletter',
  },
  {
    value: 'ted-newsletter',
    label: 'tedNews Newsletter',
  },
];

async function getMailjetMetaForSite(site) {
  if (!site) {
    return null;
  }

  const contactLists = site ? await getAllContactLists(site) : [];
  const senders = site ? await getAllSenders(site) : [];

  return {
    templates,
    contactLists: contactLists.reduce(
      (acc, { ID, IsDeleted, Name, SubscriberCount }) => {
        if (IsDeleted) {
          return acc;
        }
        return [...acc, { id: ID, name: Name, subscribers: SubscriberCount }];
      },
      []
    ),
    senders: senders.reduce((acc, { Email, ID, Name, Status }) => {
      if (Status === 'Active') {
        return [...acc, { id: ID, email: Email, name: Name }];
      }
      return acc;
    }, []),
  };
}
