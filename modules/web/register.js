import { articlesEvents } from '#modules/articles/events.js';
import { episodeEvents } from '#modules/media-library/events/episodeEvents.js';
import { lexiconEntryEvents } from '#modules/lexicon/events/lexiconEntryEvents.js';
import { showEvents } from '#modules/media-library/events/showEvents.js';

import webRouter from './routes.js';

import { webBlocksRegistration } from './blocks/blocksRegistration.js';
import { webSearchRegistration } from './search/webSearchRegistration.js';
import {
  handleArticleDeletedEvent,
  handleDisablePublisedArticlesEvent,
  handleEpisodeDeletedEvent,
  handleLexiconEntryDeletedEvent,
  handleShowDeletedEvent,
} from './services/siteSearchServices.js';
import { webDataSourceRegistration } from './data-sources/webDataSourcesRegistration.js';
import { webTasks } from './tasks/index.js';

export default function web() {
  return {
    routes: {
      '/web': webRouter,
    },

    // Register blocks
    blocks: webBlocksRegistration,

    // Register data sources
    dataSources: webDataSourceRegistration,

    // Register search indexes
    search: webSearchRegistration,

    // Register tasks
    tasks: webTasks,

    // Register listeners
    listeners: {
      // Articles
      [articlesEvents.DELETE_ARTICLE]: handleArticleDeletedEvent,
      [articlesEvents.DISABLE_ARTICLE]: handleArticleDeletedEvent,
      [articlesEvents.DISABLE_PUBLISHED_ARTICLES]:
        handleDisablePublisedArticlesEvent,

      // Episodes
      [episodeEvents.DELETE_EPISODE]: handleEpisodeDeletedEvent,
      [episodeEvents.DISABLE_EPISODE]: handleEpisodeDeletedEvent,

      // Shows
      [showEvents.DELETE_SHOW]: handleShowDeletedEvent,
      [showEvents.DISABLE_SHOW]: handleShowDeletedEvent,

      // Lexicon entries
      [lexiconEntryEvents.DELETE_LEXICON_ENTRY]: handleLexiconEntryDeletedEvent,
      [lexiconEntryEvents.DISABLE_LEXICON_ENTRY]:
        handleLexiconEntryDeletedEvent,
    },
  };
}
