import { getClientIp } from '@supercharge/request-ip/dist/index.js';

import searchIndexTaskService from '#modules/search/services/searchIndexTaskService.js';
import Logger from '#utils/logger.js';
import { toLogDate } from '#utils/dates.js';
import { verifyAuth } from '#utils/socket.js';

export const SEARCH_INDEX_TASKS_NAMESPACE = 'searchIndexTasks';

export function registerSearchIndexTasksSocket(socket) {
  const clientIp = getClientIp(socket.request);
  Logger.info(
    `[${toLogDate(new Date())}] ${clientIp} has connected to /${SEARCH_INDEX_TASKS_NAMESPACE}`
  );

  socket.on('joinTask', async (taskId) => {
    // Verify authentication using cookie from handshake
    const userId = await verifyAuth({ socket });
    if (!userId) {
      Logger.error(
        `[${toLogDate(new Date())}] ${clientIp} denied access to task ${taskId}: Invalid credentials`
      );
      socket.emit('error', {
        message: 'Unauthorized: Invalid credentials',
        status: 401,
      });
      return;
    }

    socket.join(taskId);
    Logger.info(
      `[${toLogDate(new Date())}] ${clientIp} joined task room: ${taskId}`
    );

    // Fetch and send initial task data
    try {
      const { searchIndexTask, error } =
        await searchIndexTaskService.getSearchIndexTaskById(taskId);
      if (error || !searchIndexTask) {
        socket.emit('error', {
          message: 'Task not found or error retrieving task',
        });
        return;
      }

      // Send task status
      socket.emit('taskUpdate', {
        status: searchIndexTask.status,
        startedAt: searchIndexTask.startedAt,
        finishedAt: searchIndexTask.finishedAt,
        lastIndexedAt: searchIndexTask.searchIndex?.lastIndexedAt,
      });

      // Send existing logs
      const existingLogs = searchIndexTask.details.logs || [];
      existingLogs.forEach((log) => socket.emit('log', log));
    } catch (err) {
      Logger.error(`Error fetching task ${taskId}:`, err);
      socket.emit('error', { message: 'Failed to fetch task data' });
    }
  });

  socket.on('disconnect', () => {
    Logger.info(
      `[${toLogDate(new Date())}] ${clientIp} disconnected from /${SEARCH_INDEX_TASKS_NAMESPACE}`
    );
  });
}
