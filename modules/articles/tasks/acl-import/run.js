import Entity from '#modules/entities/models/Entity.js';
import {
  logEvent,
  updateTaskStatus,
} from '#modules/tasks/services/taskRunService.js';
import { errorCodes, generateError } from '#utils/appError.js';

import articlesImport from './articles/index.js';
import getACLImportedCategoriesMap from './categories/getACLImportedCategoriesMap.js';
import categoriesImport from './categories/index.js';
import { DRY_RUN, LAST_SYNC } from './constants.js';
import getClient from './helpers/client.js';

export default async function runAclImport(task, taskRun) {
  // Overwrite task's lastSync with LAST_SYNC if is set (just for testing purposes)
  if (LAST_SYNC) {
    task.lastSync = LAST_SYNC;
  }

  const { settings } = task;

  const {
    apiUrl,
    apiToken,
    username,
    password,
    entity: targetEntity,
    entityId, // Entity ID in ACL 1.0 instance
  } = settings ?? {};

  if (!apiUrl || !apiToken || !username || !password || !entityId) {
    return {
      error: generateError(
        `Error running ACL 1.0 import (${task.id}): Task not properly configured`,
        errorCodes.TASK_ERROR,
        400
      ),
    };
  }

  await updateTaskStatus(taskRun, {
    status: 'processing',
    startedAt: new Date(),
  });

  await logEvent(taskRun, `- Setting up client for ${apiUrl}`);

  const client = await getClient({
    apiUrl,
    apiToken,
    username,
    password,
    entityId,
  });

  await logEvent(taskRun, `- Getting entity ${targetEntity}...`);
  const entity = await Entity.findById(targetEntity);

  if (!entity) {
    return {
      error: generateError(
        `Entity ${targetEntity} not found`,
        errorCodes.TASK_ERROR,
        404
      ),
    };
  }

  // Import categories
  const { error: categoriesError } = await categoriesImport({
    client,
    entity,
    task,
    dryRun: DRY_RUN,
  });

  if (categoriesError) {
    return { error: categoriesError };
  }

  // Get categories map from local categories, using ACL v1 importIDs as keys and local categories' IDs as values
  await logEvent(taskRun, `- Getting ACL-AWE categories map...`);
  const categoriesMap = await getACLImportedCategoriesMap();

  // Tags map to reduce amount of data requests to categories endpoint (it will be populated during articles import)
  const tagsMap = {};

  // Import articles
  const { error: articlesError } = await articlesImport({
    client,
    entity,
    task,
    taskRun,
    categoriesMap,
    tagsMap,
    dryRun: DRY_RUN,
  });

  if (articlesError) {
    return { error: articlesError };
  }

  return {
    // Return stats here
  };
}
