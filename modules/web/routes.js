import express from 'express';

import {
  authorizeRequest,
  protect,
} from '#modules/users/controllers/authController.js';

import siteController from './controllers/siteController.js';

import pagePresetsRouter from './pagePresetsRouter.js';
import webBackendRouter from './backendRouter.js';
import sitePagesRouter from './sitePagesRouter.js';
import siteRouter from './siteRouter.js';
import siteMenusRouter from './siteMenusRouter.js';
import siteLayoutsRouter from './siteLayoutsRouter.js';
import siteRedirectsRouter from './siteRedirectsRouter.js';
import siteAuthRouter from './siteAuthRouter.js';
import siteDataSourcesRouter from './siteDataSourcesRouter.js';
import siteNewsletterRouter from './siteNewsletterRouter.js';
import siteSearchRouter from './siteSearchRouter.js';
import automatedSitesRouter from './automatedSitesRouter.js';
import sitesRouter from './sitesRouter.js';
import webReportsRouter from './reportsRouter.js';
import pagesByResourceRouter from './pagesByResourceRouter.js';
import blockPresetsRouter from './blockPresetsRouter.js';

const router = express.Router();

// Require ClientToken and Entity
router.use(authorizeRequest());

// Require user for all routes after this middleware
router.use(protect);

// Sites routes
router.use('/sites', sitesRouter);

// Site instance routes
router.use('/sites/:siteId', siteRouter);

// Pages routes
router.use('/sites/:siteId/pages', sitePagesRouter);

// Menus routes
router.use('/sites/:siteId/menus', siteMenusRouter);

// Layouts routes
router.use('/sites/:siteId/layouts', siteLayoutsRouter);

// Redirect routes
router.use('/sites/:siteId/redirects', siteRedirectsRouter);

// Authentication routes
router.use('/sites/:siteId/auth', siteAuthRouter);

// Data Sources routes
router.use('/sites/:siteId/data-sources', siteDataSourcesRouter);

// Site newsletter routes
router.use('/sites/:siteId/newsletter', siteNewsletterRouter);

// Site search routes
router.use('/sites/:siteId/search', siteSearchRouter);

// PagePresets routes
router.use('/page-presets', pagePresetsRouter);

// BlockPresets routes
router.use('/block-presets', blockPresetsRouter);

// "Get all pages" route (used to detect pages by resource)
router.use('/pages-by-resource', pagesByResourceRouter);

// Web Backend routes (to add / remove domains) // TODO: It looks like we need to rename this route to something like "domains" or "backend-domains" for clarity.
router.use('/backend', webBackendRouter);

// Routes of available site designs in each region and hosting provider (Frontend apps)
router.route('/frontend-apps').get(siteController.getFrontendApps);

// Automated sites routes
router.use('/automated-sites', automatedSitesRouter);

// Web Reports (aka Hosting records) routes:
router.use('/reports', webReportsRouter);

export default router;
