import { errors } from '#utils/appError.js';

import taskRunService from '../services/taskRunService.js';

export const getTaskRuns = async (req, res) => {
  const { params, query } = req;
  const { taskId } = params;
  const { limit, page, skip, status } = query;

  const { taskRuns, error } = await taskRunService.getTaskRuns({
    limit,
    page,
    taskId,
    skip,
    status,
  });

  if (error) {
    throw error;
  }

  if (!taskRuns) {
    throw errors.not_found('TaskRun');
  }

  res.status(200).json(taskRuns);
};

export const getTaskRun = async (req, res) => {
  const { params } = req;
  const { taskRunId } = params;

  const { taskRun, error } = await taskRunService.getTaskRunById(taskRunId);

  if (error) {
    throw error;
  }

  if (!taskRun) {
    throw errors.not_found('TaskRun');
  }

  res.status(200).json(taskRun);
};
