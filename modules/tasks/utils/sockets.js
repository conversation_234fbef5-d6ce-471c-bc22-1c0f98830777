import { getClientIp } from '@supercharge/request-ip/dist/index.js';

import { toLogDate } from '#utils/dates.js';
import { logError, logInfo } from '#utils/logger.js';
import { verifyAuth } from '#utils/socket.js';

import { getTaskRunById } from '../services/taskRunService.js';

export const TASK_RUNS_NAMESPACE = 'taskRuns';

export function registerTaskRunsSocket(socket) {
  const clientIp = getClientIp(socket.request);
  logInfo(
    `[${toLogDate(new Date())}] ${clientIp} has connected to /${TASK_RUNS_NAMESPACE}`
  );

  socket.on('joinTaskRun', async (taskRunId) => {
    // Verify authentication using cookie from handshake
    const userId = await verifyAuth({ socket });
    if (!userId) {
      logError(
        `[${toLogDate(new Date())}] ${clientIp} denied access to task ${taskRunId}: Invalid credentials`
      );
      socket.emit('error', {
        message: 'Unauthorized: Invalid credentials',
        status: 401,
      });
      return;
    }

    socket.join(taskRunId);
    logInfo(
      `[${toLogDate(new Date())}] ${clientIp} joined task run room: ${taskRunId}`
    );

    // Fetch and send initial task data
    try {
      const { taskRun, error } = await getTaskRunById(taskRunId);
      if (error || !taskRun) {
        socket.emit('error', {
          message: 'Task run not found or error retrieving task',
        });
        return;
      }

      // Send task status
      socket.emit('taskRunUpdate', {
        status: taskRun.status,
        startedAt: taskRun.startedAt,
        finishedAt: taskRun.finishedAt,
        lastSync: taskRun.task?.lastSync,
      });

      // Send existing logs
      const existingLogs = taskRun.details.logs || [];
      existingLogs.forEach((log) => socket.emit('log', log));
    } catch (err) {
      logError(`Error fetching task run ${taskRunId}:`, err);
      socket.emit('error', { message: 'Failed to fetch task run data' });
    }
  });

  socket.on('disconnect', () => {
    logInfo(
      `[${toLogDate(new Date())}] ${clientIp} disconnected from /${TASK_RUNS_NAMESPACE}`
    );
  });
}
