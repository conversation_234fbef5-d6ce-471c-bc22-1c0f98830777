import { DateTime } from 'luxon';
import Mailjet from 'node-mailjet';

import { decrypt, encrypt } from '#utils/encryption.js';
import { logError } from '#utils/logger.js';
import { errorCodes, errors, generateError } from '#utils/appError.js';

export function encryptMailjetProvider(newsletter, prevNewsletter) {
  return {
    ...newsletter,
    settings: {
      ...newsletter.settings,
      secretKey: newsletter.settings?.secretKey
        ? encrypt(newsletter.settings.secretKey)
        : prevNewsletter?.settings?.secretKey,
    },
  };
}

export function decryptMailjetProvider(newsletter) {
  return {
    ...newsletter,
    settings: {
      ...newsletter.settings,
      secretKey: '', // Once set, this should not be shown, but rather just reset
    },
  };
}

function getMailjetClient(site) {
  if (!site.newsletter) {
    logError(
      `Error initialising Mailjet client for site: ${site.title}`,
      'Site newsletter not configured'
    );
  }
  if (site.newsletter.provider !== 'mailjet') {
    logError(
      `Error initialising Mailjet client for site: ${site.title}`,
      'Site newsletter provider not configured for Mailjet'
    );
  }

  const { apiKey, secretKey: encryptedApiSecret } = site.newsletter.settings;

  const apiSecret = decrypt(encryptedApiSecret);
  return Mailjet.apiConnect(apiKey, apiSecret);
}

/**
 * Generates a new Mailjet campaign draft. See [Mailjet docs](https://dev.mailjet.com/email/reference/campaigns/drafts/#v3_post_campaigndraft) for more info.
 *
 * @param {Object} site The site object
 * @param {Object} [taskSettings] The task settings object
 * @param {String} taskSettings.replyEmail The reply email address for the campaign
 * @param {String} taskSettings.senderName The sender name for the campaign
 * @param {String} taskSettings.sender The sender ID for the campaign
 * @param {String} taskSettings.campaignName The name of the campaign
 * @param {String} taskSettings.contactList The contact list ID for the campaign
 * @param {String} taskSettings.subject The subject of the campaign
 * @param {Boolean} taskSettings.includeDateInSubject Whether to include the date in the subject line
 * @returns {Object} The first campaign draft returned by the Mailjet API
 */
export async function createCampaignDraft(site, taskSettings = {}) {
  const {
    replyEmail,
    senderName,
    sender: senderId,
    campaignName,
    contactList,
    subject,
    includeDateInSubject,
  } = taskSettings;

  try {
    const sender = await getSender(site, senderId);

    if (!sender) {
      return {
        error: generateError(
          `Mailjet sender not found for ID ${senderId}`,
          errorCodes.MAILJET_SENDER_NOT_FOUND,
          400
        ),
      };
    }

    const dateSent = DateTime.now()
      .setLocale(site.language ?? 'en_US')
      .toLocaleString(DateTime.DATE_FULL);

    const request = {
      EditMode: 'html2',
      IsStarred: false,
      IsTextPartIncluded: true,
      ReplyEmail: replyEmail,
      SenderName: senderName,
      Title: `${new Date().toISOString()} ${campaignName}`,
      ContactsListID: contactList,
      Locale: site.language ?? 'en_US',
      Sender: sender.ID,
      SenderEmail: sender.Email,
      Subject: includeDateInSubject ? `${subject} ${dateSent}` : subject,
    };

    const mailjetClient = getMailjetClient(site);
    const result = await mailjetClient
      .post('campaigndraft', { version: 'v3' })
      .request(request);

    if (result.body.Count >= 1) {
      return { campaignDraft: result.body.Data[0] };
    }

    return { error: errors.task_error() };
  } catch (e) {
    if (e.code && e.statusText && e.statusCode) {
      return { error: generateError(e.statusText, e.code, e.statusCode) };
    }

    return {
      error: errors.internal_error(),
    };
  }
}

/**
 * Generates the content for a Mailjet campaign draft.
 * See [Mailjet docs](https://dev.mailjet.com/email/reference/campaigns/drafts/#v3_post_campaigndraft_draft_ID_detailcontent) for more info. *
 * @param {Object} options
 * @param {String} options.draftId The ID of the campaign draft
 * @param {Object} options.request The request object containing the content to set
 * @param {Object} options.site The site object
 * @returns {Promise<Object>} The result of the Mailjet API request or an error object
 */
export async function createCampaignDraftContent({ draftId, request, site }) {
  try {
    const mailjetClient = getMailjetClient(site);
    const result = await mailjetClient
      .post('campaigndraft', { version: 'v3' })
      .id(draftId)
      .action('detailcontent')
      .request(request);

    return {
      result,
    };
  } catch (e) {
    if (e.code && e.statusText && e.statusCode) {
      return { error: generateError(e.statusText, e.code, e.statusCode) };
    }
    return { error: errors.internal_error() };
  }
}

/**
 * Sends a campaign draft immediately. To test the send, you can define the `action` as `test`.
 *   - Read more on the [send action](https://dev.mailjet.com/email/reference/campaigns/drafts/#v3_post_campaigndraft_draft_ID_send)
 *   - Read more on the [test action](https://dev.mailjet.com/email/reference/campaigns/drafts/#v3_post_campaigndraft_draft_ID_test)
 *
 * @param {Object} options
 * @param {String} options.action Either `send` or `test`
 * @param {String} options.draftId The ID of the campaign draft to send
 * @param {Object} options.request If action === 'test', provide the required request object
 * @param {Object} options.site The site object
 * @returns {Promise<Object>} Resolves when the campaign draft is sent or tested successfully, or returns with an error.
 */
export async function sendCampaignDraft({
  action = 'send',
  draftId,
  request,
  site,
}) {
  try {
    const mailjetClient = getMailjetClient(site);
    const result = await mailjetClient
      .post('campaigndraft', { version: 'v3' })
      .id(draftId)
      .action(action)
      .request(request);

    return {
      result,
    };
  } catch (e) {
    if (e.code && e.statusText && e.statusCode) {
      return { error: generateError(e.statusText, e.code, e.statusCode) };
    }
    return { error: errors.internal_error() };
  }
}

export async function getAllContactLists(site) {
  try {
    const mailjetClient = getMailjetClient(site);
    const result = await mailjetClient
      .get('contactslist', { version: 'v3' })
      .request();
    return result.body.Data;
  } catch (e) {
    if (e.code && e.statusText && e.statusCode) {
      throw generateError(e.statusText, e.code, e.statusCode);
    } else {
      throw errors.internal_error();
    }
  }
}

export async function getAllSenders(site) {
  try {
    const mailjetClient = getMailjetClient(site);
    const result = await mailjetClient
      .get('sender', { version: 'v3' })
      .request();
    return result.body.Data;
  } catch (e) {
    if (e.code && e.statusText && e.statusCode) {
      throw generateError(e.statusText, e.code, e.statusCode);
    } else {
      throw errors.internal_error();
    }
  }
}

export async function getSender(site, senderId) {
  try {
    const mailjetClient = getMailjetClient(site);
    const result = await mailjetClient
      .get('sender', { version: 'v3' })
      .id(senderId)
      .request();
    return result.body.Data[0];
  } catch (e) {
    if (e.code && e.statusText && e.statusCode) {
      throw generateError(e.statusText, e.code, e.statusCode);
    } else {
      throw errors.internal_error();
    }
  }
}
