import { DateTime } from 'luxon';

import Article from '#modules/articles/models/Article.js';
import { logEvent } from '#modules/tasks/services/taskRunService.js';

import { DRY_RUN, IMPORT_BATCH_SIZE, IMPORT_TYPE } from '../constants.js';
import getArticles, { getTotalArticles } from './getArticles.js';
import importArticle from './importArticle.js';

/**
 * Import articles from old ACL API
 * @param {Object} params
 * @param {Object} params.client - Axios client
 * @param {Object} params.task - Task data
 * @param {Object} params.taskRun - Task run instance
 * @param {Object} params.entity - AWE's target Entity
 * @param {Object} params.categoriesMap - Map of categories
 * @param {Boolean} params.dryRun - Indicates if the import is a dry run or not
 */
export default async function importArticles({
  client,
  task,
  taskRun,
  entity,
  categoriesMap,
  tagsMap,
}) {
  const { settings, lastSync } = task;

  const {
    remoteSiteId, // Site ID in ACL 1.0 instance
    articleContentTypeId, // Current Articles's ContentType ID in ACL 1.0 instance
    localSiteId, // Site ID in AWE
    canonicalSitePage, // Canonical page in AWE
    flagMappings, // Map of flags
  } = settings ?? {};

  const fromDateString = settings.fromDate ?? '';
  const toDateString = settings.toDate ?? '';

  const fromDate = fromDateString
    ? DateTime.fromISO(fromDateString).toJSDate()
    : undefined;
  const toDate = toDateString
    ? DateTime.fromISO(toDateString).toJSDate()
    : undefined;

  if (fromDate && toDate && fromDate > toDate) {
    await logEvent(
      taskRun,
      `Invalid date range: "fromDate" (${fromDate.toISOString()}) is greater than "toDate" (${toDate.toISOString()})`,
      'error'
    );
    return { error: 'Invalid date range' };
  }

  // Map of flags from ACL 1.0 to AWE
  const flagsMap = flagMappings.reduce((acc, { remote, local }) => {
    if (remote && local) acc[remote] = local; // Only add the mapping if both remote and local are defined
    return acc;
  }, {});

  await logEvent(taskRun, `- Getting total articles...`);

  const { total } = await getTotalArticles({
    client,
    contentTypeId: articleContentTypeId,
    lastSync,
    fromDate,
    toDate,
  });

  await logEvent(taskRun, '- Importing articles...');

  const totalPages = Math.ceil(total / IMPORT_BATCH_SIZE);

  await logEvent(
    taskRun,
    `- Posts to import: ${total}. Import batches: ${totalPages}.`
  );

  let currentPage = 1;

  const stats = {
    imported: 0, // new original articles
    translated: 0, // new translations
    updated: 0, // includes both originals and translations
    skipped: 0, // skipped articles (already imported an up-to-date)
  };

  // Object to store articles with related data.
  // Schema: { <AWEArticleId>: [AclRelatedArticleId1, AclRelatedArticleId2, ...] }
  const relatedArticlesdMap = {};

  while (currentPage <= totalPages) {
    await logEvent(taskRun, `- Importing batch ${currentPage} / ${totalPages}`);

    const { data: articles, error } = await getArticles({
      client, // Axios client
      contentTypeId: articleContentTypeId, // Articles's ContentType ID
      lastSync, // Last sync date
      fromDate, // From date
      toDate, // To date
      limit: IMPORT_BATCH_SIZE, // Items per batch
      page: currentPage, // Current batch
    });

    if (error) {
      await logEvent(
        taskRun,
        ['Error when trying to get articles:', error.toString()],
        'error'
      );
      return { error };
    }

    // get first date of the batch
    const firstArticleDate = articles?.[0]?.firstApprovedAt;

    // get last date of the batch
    const lastArticleDate = articles?.[articles.length - 1]?.firstApprovedAt;

    await logEvent(
      taskRun,
      `  - Batch dates: ${firstArticleDate} - ${lastArticleDate} (${articles.length} articles)`
    );

    const promises = articles?.map(
      async (article) =>
        await importArticle({
          client, // Axios client
          article, // Article data
          entity, // AWE's target Entity
          remoteSiteId, // ACL 1.0's Site ID
          localSiteId, // AWE's target Site ID to publish the article to
          canonicalSitePage, // AWE's Canonical page
          flagsMap, // Map of ACL 1.0's flags to AWE's flags
          categoriesMap, // Map of categories
          tagsMap, // Map of tags (for reuse)
          relatedArticlesdMap, // Map of related articles
          lastSync, // Last sync date
          fromDate, // From date
          toDate, // To date
          stats, // Stats object
        })
    );

    await Promise.all(promises);

    currentPage += 1;

    if (error) {
      await logEvent(
        taskRun,
        ['Error when trying to get articles:', error.toString()],
        'error'
      );
      return { error };
    }
  }

  // Parse related articles and update articles with related ones
  for (const [aweArticleId, aclRelatedArticlesIds] of Object.entries(
    relatedArticlesdMap
  )) {
    if (DRY_RUN) {
      await logEvent(
        taskRun,
        `- Dry run: Would update article ${aweArticleId} with related articles:`,
        aclRelatedArticlesIds
      );
      continue;
    }

    // Get local AWE's article
    const article = await Article.findById(aweArticleId);

    if (!article) {
      await logEvent(taskRun, `Article not found: ${aweArticleId}`, 'error');
      continue;
    }

    // Get related articles from AWE
    const relatedArticles = await Article.find({
      'language': 'en', // Only originals in English, to avoid relate to translations
      'importIDs.0.type': IMPORT_TYPE, // From ACL 1.0
      'importIDs.0.recordID': { $in: aclRelatedArticlesIds }, // with related IDs
    });

    // Get related articles IDs
    const relatedArticlesIds = relatedArticles.map((a) => a._id);

    // Update article with related articles
    await article.updateOne({ related: relatedArticlesIds });
  }

  await logEvent(
    taskRun,
    `- Articles import stats: Imported: ${stats.imported} (Translations: ${stats.translated}), Updated: ${stats.updated}, Skipped: ${stats.skipped}.`
  );

  return {
    stats,
  };
}
