import { createExternalApiClient } from '#utils/kyClient.js';
import Logger from '#utils/logger.js';

/**
 * Get sitemap paths for site
 * @param {Object} options
 * @param {Array} options.resources - The resources to get sitemap paths for
 * @param {Object} options.site - The site associated with the block
 * @param {String} options.updatedSince - The updated since date
 * @returns {Object}
 */
export async function getSiteMapPathsForSite({
  resources,
  site,
  updatedSince,
}) {
  // Create ky client for API requests
  const client = createExternalApiClient({
    prefixUrl: process.env.PUBLIC_API_URL,
    headers: {
      ClientToken: process.env.PUBLIC_API_CLIENT_TOKEN,
      Origin: site.domain,
    },
  });

  const fetchPromises = ['Page', ...resources].map(async (resource) => {
    try {
      const searchParams = {
        resource,
        filterMode: 'index', // Only get indexable paths
      };

      if (updatedSince) {
        searchParams.updatedSince = updatedSince;
      }

      return await client.get('web/sitemap', { searchParams }).json();
    } catch (error) {
      Logger.error('  Error fetching sitemap paths for site', error);
      return Promise.reject(error);
    }
  });

  const paths = await Promise.all(fetchPromises);
  const validPaths = paths
    .filter(Boolean)
    .flatMap((path) => path.items?.filter(Boolean))
    .reduce((acc, { path, translations }) => {
      if (translations) {
        translations.forEach(({ locale, path: translationPath }) => {
          if (!acc[locale]) {
            acc[locale] = new Set();
          }
          acc[locale].add(translationPath);
        });
      } else {
        if (!acc[site.language]) {
          acc[site.language] = new Set();
        }
        acc[site.language].add(path);
      }
      return acc;
    }, {});

  return { paths: validPaths };
}
