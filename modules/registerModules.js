import { eventEmitter } from '#utils/eventEmitter.js';

export default function registerModules(app, modules) {
  const blocks = {};
  const dataSources = {};
  const modelResources = {};
  const searchIndexes = {};
  const sockets = {};
  const tasks = {};

  // Register the modules
  modules.forEach((moduleFn) => {
    // Load the module. Uses a function to allow for params to be passed in
    const module = moduleFn();

    // Register routes
    if (module.routes) {
      Object.entries(module.routes).forEach(([route, router]) => {
        app.use(`/v1${route}`, router);
      });
    }

    // Register event listeners
    if (module.listeners) {
      Object.entries(module.listeners).forEach(([key, listener]) => {
        eventEmitter.on(key, listener);
      });
    }

    // Register data sources
    if (module.dataSources) {
      Object.entries(module.dataSources).forEach(
        ([dataSourceName, dataSource]) => {
          dataSources[dataSourceName] = dataSource;
        }
      );
    }

    // Register search indexes
    if (module.search && module.search.indexes) {
      Object.entries(module.search.indexes).forEach(([indexName, index]) => {
        searchIndexes[indexName] = index;
      });
    }

    // Register blocks
    if (module.blocks) {
      Object.entries(module.blocks).forEach(([blockName, block]) => {
        blocks[blockName] = block;
      });
    }

    // Register modelResources
    if (module.modelResources) {
      Object.entries(module.modelResources).forEach(
        ([modelResourceName, modelResource]) => {
          modelResources[modelResourceName] = modelResource;
        }
      );
    }

    // Register tasks
    if (module.tasks) {
      Object.entries(module.tasks).forEach(([taskName, task]) => {
        tasks[taskName] = task;
      });
    }

    // Register sockets
    if (module.sockets) {
      Object.entries(module.sockets).forEach(
        ([socketNamespace, socketInitFn]) => {
          sockets[socketNamespace] = socketInitFn;
        }
      );
    }
  });

  // Set the data sources available in the app.
  app.set('dataSources', dataSources);
  // Set the search indexes available in the app.
  app.set('searchIndexes', searchIndexes);
  // Set the blocks available in the app.
  app.set('blocks', blocks);
  // Set the modelResources available in the app.
  app.set('modelResources', modelResources);
  // Set the sockets available in the app.
  app.set('sockets', sockets);
  // Set the tasks available in the app.
  app.set('tasks', tasks);
}
