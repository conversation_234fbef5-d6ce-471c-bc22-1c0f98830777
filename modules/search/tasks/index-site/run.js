import Entity from '#modules/entities/models/Entity.js';
import searchIndexService from '#modules/search/services/searchIndexService.js';
import * as siteSearchServices from '#modules/web/services/siteSearchServices.js';
import {
  logEvent,
  updateTaskStatus,
} from '#modules/tasks/services/taskRunService.js';
import { errorCodes, generateError } from '#utils/appError.js';

export async function runSiteIndex(task, taskRun) {
  const { site } = task?.settings ?? {};

  if (!site) {
    return {
      error: generateError(
        `Error running Site Indexing task (${task.id}): Task not properly configured, missing site`,
        errorCodes.TASK_ERROR,
        400
      ),
    };
  }

  const entity = await Entity.findById(task.entity, 'id name');

  const { error: searchIndexesError, items: siteSearchIndexes } =
    await searchIndexService.getSearchIndexes({
      entityId: entity._id,
      siteId: site,
      limit: 0,
    });

  if (searchIndexesError) {
    return {
      error: generateError(
        `Error running Site Indexing task (${task.id}): Could not retrieve site search indexes`,
        errorCodes.TASK_ERROR,
        searchIndexesError.status || 400
      ),
    };
  }

  await updateTaskStatus(taskRun, {
    status: 'processing',
    startedAt: new Date(),
  });

  const promises = siteSearchIndexes.map(async (siteSearchIndex) => {
    const { tasks, error } = await siteSearchServices.reIndexSiteSearchIndex({
      entity,
      searchIndexId: siteSearchIndex._id,
      siteId: site,
      useLastSync: true,
    });

    if (error) {
      return Promise.reject(error);
    }

    return tasks;
  });

  await Promise.all(promises);

  await logEvent(
    taskRun,
    'Site Indexing tasks triggered successfully. Check the site indexing tasks for progress.'
  );
}
